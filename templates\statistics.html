<!DOCTYPE html>
<html>
<head>
    <title>Statistics - File Processing Pipeline</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #3498db;
        }
        
        .stat-card.overdue {
            border-left-color: #e74c3c;
        }
        
        .stat-card.success {
            border-left-color: #27ae60;
        }
        
        .stat-card.warning {
            border-left-color: #f39c12;
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #7f8c8d;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .chart-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        
        .chart-wrapper {
            position: relative;
            height: 300px;
        }
        
        .table-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            overflow-x: auto;
        }
        
        .stats-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        
        .stats-table th,
        .stats-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .stats-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .stats-table tr:hover {
            background-color: #f8f9fa;
        }
        
        .overdue-indicator {
            color: #e74c3c;
            font-weight: bold;
        }
        
        .time-format {
            font-family: monospace;
            font-size: 0.9em;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
            margin: 5px 0;
        }
        
        .progress-fill {
            height: 100%;
            background-color: #3498db;
            transition: width 0.3s ease;
        }
        
        .progress-fill.overdue {
            background-color: #e74c3c;
        }
        
        .grid-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        @media (max-width: 768px) {
            .grid-2 {
                grid-template-columns: 1fr;
            }
            
            .stats-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>File Processing Pipeline - Statistics</h1>
        <div class="user-info">
            <span>Welcome, {{ username }}</span>
            <a href="{{ url_for('logout') }}" class="btn btn-small">Logout</a>
            <a href="{{ url_for('index') }}" class="btn btn-small">Back to Overview</a>
        </div>
    </div>

    <div class="container">
        <div class="actions-bar">
            <h2>System Statistics</h2>
            <div>
                <button onclick="window.print()" class="btn btn-secondary">Print Report</button>
                <button onclick="location.reload()" class="btn">Refresh Data</button>
            </div>
        </div>

        <!-- Overview Statistics -->
        <div class="stats-container">
            <div class="stat-card">
                <div class="stat-number">{{ stats.total_files }}</div>
                <div class="stat-label">Total Files</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ stats.total_users }}</div>
                <div class="stat-label">Total Users</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ stats.total_steps }}</div>
                <div class="stat-label">Total Steps</div>
            </div>
            <div class="stat-card overdue">
                <div class="stat-number">{{ stats.overdue_stats.total_overdue_files }}</div>
                <div class="stat-label">Overdue Files</div>
            </div>
            <div class="stat-card overdue">
                <div class="stat-number">{{ stats.overdue_stats.total_overdue_steps }}</div>
                <div class="stat-label">Overdue Steps</div>
            </div>
            <div class="stat-card success">
                <div class="stat-number time-format">{{ '%02d:%02d:%02d'|format(stats.time_stats.total_time_worked // 1440, (stats.time_stats.total_time_worked % 1440) // 60, stats.time_stats.total_time_worked % 60) }}</div>
                <div class="stat-label">Total Time Worked</div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="grid-2">
            <div class="chart-container">
                <div class="chart-title">Files by Current Step</div>
                <div class="chart-wrapper">
                    <canvas id="stepDistributionChart"></canvas>
                </div>
            </div>
            
            <div class="chart-container">
                <div class="chart-title">Overdue Steps by Type</div>
                <div class="chart-wrapper">
                    <canvas id="overdueStepsChart"></canvas>
                </div>
            </div>
        </div>

        <div class="grid-2">
            <div class="chart-container">
                <div class="chart-title">Files by Supplier</div>
                <div class="chart-wrapper">
                    <canvas id="supplierChart"></canvas>
                </div>
            </div>
            
            <div class="chart-container">
                <div class="chart-title">Time Worked by Step</div>
                <div class="chart-wrapper">
                    <canvas id="timeByStepChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Step Statistics Table -->
        <div class="table-container">
            <div class="chart-title">Step Performance Statistics</div>
            <table class="stats-table">
                <thead>
                    <tr>
                        <th>Step</th>
                        <th>Total Files</th>
                        <th>Completed</th>
                        <th>In Progress</th>
                        <th>Not Started</th>
                        <th>Overdue</th>
                        <th>Avg Time</th>
                        <th>Total Time</th>
                        <th>Assigned Users</th>
                    </tr>
                </thead>
                <tbody>
                    {% for step, data in stats.step_stats.items() %}
                    <tr>
                        <td><strong>{{ step|capitalize }}</strong></td>
                        <td>{{ data.total_files }}</td>
                        <td>{{ data.completed_files }}</td>
                        <td>{{ data.in_progress_files }}</td>
                        <td>{{ data.not_started_files }}</td>
                        <td class="{% if data.overdue_files > 0 %}overdue-indicator{% endif %}">
                            {{ data.overdue_files }}
                            {% if data.overdue_files > 0 %}⚠️{% endif %}
                        </td>
                        <td class="time-format">{{ '%02d:%02d:%02d'|format(data.average_time // 1440, (data.average_time % 1440) // 60, data.average_time % 60) }}</td>
                        <td class="time-format">{{ '%02d:%02d:%02d'|format(data.total_time_worked // 1440, (data.total_time_worked % 1440) // 60, data.total_time_worked % 60) }}</td>
                        <td>{{ data.assigned_users }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- User Statistics Table -->
        <div class="table-container">
            <div class="chart-title">User Performance Statistics</div>
            <table class="stats-table">
                <thead>
                    <tr>
                        <th>User</th>
                        <th>Files Worked</th>
                        <th>Completed Steps</th>
                        <th>In Progress</th>
                        <th>Overdue Steps</th>
                        <th>Total Time</th>
                        <th>Avg Time/File</th>
                        <th>Assigned Steps</th>
                    </tr>
                </thead>
                <tbody>
                    {% for username, data in stats.user_stats.items() %}
                    <tr>
                        <td><strong>{{ username }}</strong></td>
                        <td>{{ data.total_files_worked }}</td>
                        <td>{{ data.completed_steps }}</td>
                        <td>{{ data.in_progress_steps }}</td>
                        <td class="{% if data.overdue_steps > 0 %}overdue-indicator{% endif %}">
                            {{ data.overdue_steps }}
                            {% if data.overdue_steps > 0 %}⚠️{% endif %}
                        </td>
                        <td class="time-format">{{ '%02d:%02d:%02d'|format(data.total_time_worked // 1440, (data.total_time_worked % 1440) // 60, data.total_time_worked % 60) }}</td>
                        <td class="time-format">{{ '%02d:%02d:%02d'|format(data.average_time_per_file // 1440, (data.average_time_per_file % 1440) // 60, data.average_time_per_file % 60) }}</td>
                        <td>{{ data.assigned_steps|join(', ') if data.assigned_steps else 'None' }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // Chart.js configuration
        Chart.defaults.font.family = 'Arial, sans-serif';
        Chart.defaults.color = '#2c3e50';

        // Files by Current Step Chart
        const stepDistributionCtx = document.getElementById('stepDistributionChart').getContext('2d');
        new Chart(stepDistributionCtx, {
            type: 'doughnut',
            data: {
                labels: [{% for step, count in stats.file_distribution.by_step.items() %}'{{ step|capitalize }}'{% if not loop.last %},{% endif %}{% endfor %}],
                datasets: [{
                    data: [{% for step, count in stats.file_distribution.by_step.items() %}{{ count }}{% if not loop.last %},{% endif %}{% endfor %}],
                    backgroundColor: [
                        '#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6',
                        '#1abc9c', '#34495e', '#e67e22', '#95a5a6', '#f1c40f'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });

        // Overdue Steps Chart
        const overdueStepsCtx = document.getElementById('overdueStepsChart').getContext('2d');
        new Chart(overdueStepsCtx, {
            type: 'bar',
            data: {
                labels: [{% for step, count in stats.overdue_stats.overdue_by_step.items() %}'{{ step|capitalize }}'{% if not loop.last %},{% endif %}{% endfor %}],
                datasets: [{
                    label: 'Overdue Steps',
                    data: [{% for step, count in stats.overdue_stats.overdue_by_step.items() %}{{ count }}{% if not loop.last %},{% endif %}{% endfor %}],
                    backgroundColor: '#e74c3c',
                    borderColor: '#c0392b',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });

        // Files by Supplier Chart
        const supplierCtx = document.getElementById('supplierChart').getContext('2d');
        new Chart(supplierCtx, {
            type: 'pie',
            data: {
                labels: [{% for supplier, data in stats.supplier_stats.items() %}'{{ supplier }}'{% if not loop.last %},{% endif %}{% endfor %}],
                datasets: [{
                    data: [{% for supplier, data in stats.supplier_stats.items() %}{{ data.total_files }}{% if not loop.last %},{% endif %}{% endfor %}],
                    backgroundColor: [
                        '#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6',
                        '#1abc9c', '#34495e', '#e67e22', '#95a5a6', '#f1c40f'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });

        // Time Worked by Step Chart
        const timeByStepCtx = document.getElementById('timeByStepChart').getContext('2d');
        new Chart(timeByStepCtx, {
            type: 'bar',
            data: {
                labels: [{% for step, data in stats.step_stats.items() %}'{{ step|capitalize }}'{% if not loop.last %},{% endif %}{% endfor %}],
                datasets: [{
                    label: 'Hours Worked',
                    data: [{% for step, data in stats.step_stats.items() %}{{ (data.total_time_worked / 60)|round(1) }}{% if not loop.last %},{% endif %}{% endfor %}],
                    backgroundColor: '#3498db',
                    borderColor: '#2980b9',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Hours'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });

        // Auto-refresh every 5 minutes
        setTimeout(function() {
            location.reload();
        }, 300000);
    </script>
</body>
</html>
