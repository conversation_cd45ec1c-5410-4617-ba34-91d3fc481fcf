<!DOCTYPE html>
<html>
<head>
    <title>File Processing Pipeline</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <style>
        .overdue {
            color: #e74c3c;
            font-weight: bold;
        }

        .overdue-indicator {
            color: #e74c3c;
            font-size: 1.2em;
            margin-left: 5px;
            animation: pulse 1s infinite;
        }

        .assigned-time {
            font-size: 0.85em;
            color: #666;
            margin-left: 5px;
        }

        @keyframes pulse {
            0% { opacity: 0.5; }
            50% { opacity: 1; }
            100% { opacity: 0.5; }
        }

        .btn-primary {
            background-color: #3498db;
            color: white;
            font-weight: bold;
        }

        .btn-primary:hover {
            background-color: #2980b9;
        }

        .btn-secondary {
            background-color: #95a5a6;
            color: white;
            font-weight: bold;
        }

        .btn-secondary:hover {
            background-color: #7f8c8d;
        }

        /* Notification styles */
        .notification-container {
            position: relative;
            display: inline-block;
        }

        .notification-btn {
            position: relative;
            background: #3498db;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }

        .notification-btn:hover {
            background: #2980b9;
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 12px;
            font-weight: bold;
            min-width: 18px;
            text-align: center;
            display: none;
        }

        .notification-badge.show {
            display: block;
        }

        .notification-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            width: 350px;
            max-height: 400px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .notification-dropdown.show {
            display: block;
        }

        .notification-header {
            padding: 15px;
            border-bottom: 1px solid #eee;
            background: #f8f9fa;
            border-radius: 5px 5px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .notification-header h3 {
            margin: 0;
            font-size: 16px;
            color: #333;
        }

        .mark-all-read-btn {
            background: #27ae60;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }

        .mark-all-read-btn:hover {
            background: #219a52;
        }

        .notification-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .notification-item {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background 0.2s;
        }

        .notification-item:hover {
            background: #f8f9fa;
        }

        .notification-item.unread {
            background: #e8f4fd;
            border-left: 3px solid #3498db;
        }

        .notification-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
            font-size: 14px;
        }

        .notification-message {
            color: #666;
            font-size: 13px;
            line-height: 1.4;
            margin-bottom: 5px;
        }

        .notification-time {
            color: #999;
            font-size: 11px;
        }

        .notification-actions {
            margin-top: 8px;
            display: flex;
            gap: 8px;
        }

        .mark-read-btn {
            background: #95a5a6;
            color: white;
            border: none;
            padding: 3px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 11px;
        }

        .mark-read-btn:hover {
            background: #7f8c8d;
        }

        .open-file-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 3px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 11px;
        }

        .open-file-btn:hover {
            background: #2980b9;
        }

        .download-prev-btn {
            background: #f39c12;
            color: white;
            border: none;
            padding: 3px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 11px;
        }

        .download-prev-btn:hover {
            background: #e67e22;
        }

        .upload-btn {
            background: #27ae60;
            color: white;
            border: none;
            padding: 3px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 11px;
        }

        .upload-btn:hover {
            background: #219a52;
        }

        .no-notifications {
            padding: 20px;
            text-align: center;
            color: #999;
            font-style: italic;
        }

        /* Upload modal styles */
        .upload-modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .upload-modal-content {
            background-color: white;
            margin: 10% auto;
            padding: 20px;
            border-radius: 5px;
            width: 500px;
            max-width: 90%;
        }

        .upload-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }

        .upload-modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #999;
        }

        .upload-modal-close:hover {
            color: #333;
        }

        .upload-form-group {
            margin-bottom: 15px;
        }

        .upload-form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .upload-form-group input,
        .upload-form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
            box-sizing: border-box;
        }

        .upload-form-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 20px;
        }

        .upload-form-actions button {
            padding: 8px 16px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }

        .upload-submit-btn {
            background: #27ae60;
            color: white;
        }

        .upload-submit-btn:hover {
            background: #219a52;
        }

        .upload-cancel-btn {
            background: #95a5a6;
            color: white;
        }

        .upload-cancel-btn:hover {
            background: #7f8c8d;
        }

        /* Loading spinner */
        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
            vertical-align: middle;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .loading-text {
            display: none;
        }

        .is-loading .loading-text {
            display: inline;
        }

        .is-loading .normal-text {
            display: none;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>File Processing Pipeline</h1>
        <div class="user-info">
            <span>Welcome, {{ username }}</span>

            <!-- Notification Button -->
            <div class="notification-container">
                <button class="notification-btn" id="notification-btn">
                    🔔
                    <span class="notification-badge" id="notification-badge">0</span>
                </button>
                <div class="notification-dropdown" id="notification-dropdown">
                    <div class="notification-header">
                        <h3>Notifications</h3>
                        <button class="mark-all-read-btn" id="mark-all-read-btn">Mark All Read</button>
                    </div>
                    <div class="notification-list" id="notification-list">
                        <div class="no-notifications">Loading notifications...</div>
                    </div>
                </div>
            </div>

            <!--<a href="{{ url_for('statistics') }}" class="btn btn-small">Statistics</a>-->
            <a href="{{ url_for('logout') }}" class="btn btn-small">Logout</a>
            {% if is_admin %}
            <a href="{{ url_for('register') }}" class="btn btn-small">Register User</a>
            <a href="{{ url_for('manage_users') }}" class="btn btn-small">Manage Users</a>
            <a href="{{ url_for('manage_steps') }}" class="btn btn-small">Manage Steps</a>
            <a href="{{ url_for('manage_suppliers') }}" class="btn btn-small">Manage Suppliers</a>
            <a href="{{ url_for('manage_process_types') }}" class="btn btn-small">Manage Process Types</a>
            {% endif %}
        </div>
    </div>

    {% with messages = get_flashed_messages() %}
    {% if messages %}
    <div class="flash-messages">
        {% for message in messages %}
        <div class="flash-message">{{ message }}</div>
        {% endfor %}
    </div>
    {% endif %}
    {% endwith %}

    <div class="container">
        <div class="actions-bar">
            <h2>Files Overview</h2>
            <div>
                <button id="upload-new-btn" class="btn">Upload New File</button>
                <button id="download-previous-step-btn" class="btn btn-secondary">Download Previous Step Files</button>
                {% if is_admin %}
                <button id="save-all-data-btn" class="btn btn-primary">Save All Data</button>
                {% endif %}
            </div>
        </div>

        <div id="upload-form-container" class="hidden modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Upload New File</h2>
                    <span class="close-modal">&times;</span>
                </div>
                <form action="/upload" method="post" enctype="multipart/form-data" id="upload-form">
                    <div class="form-group">
                        <label for="supplier">Supplier:</label>
                        <input type="text" id="supplier" name="supplier" required>
                    </div>
                    <div class="form-group">
                        <label for="process_type">Process Type:</label>
                        <select id="process_type" name="process_type">
                            {% for type in process_types %}
                            <option value="{{ type }}">{{ type }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="step">Initial Step:</label>
                        <select id="step" name="step">
                            {% for step in steps %}
                            <option value="{{ step }}">{{ step|capitalize }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="file_id">File ID (for existing file):</label>
                        <input type="text" id="file_id" name="file_id" placeholder="Leave empty for new file">
                    </div>
                    <div class="form-group">
                        <label for="file">File:</label>
                        <input type="file" id="file" name="file" required>
                    </div>
                    <div class="form-actions">
                        <button type="submit" id="upload-submit-btn" class="btn">
                            <span class="spinner" style="display: none;"></span>
                            <span class="normal-text">Upload</span>
                            <span class="loading-text">Uploading...</span>
                        </button>
                        <button type="button" class="btn btn-secondary close-modal-btn">Cancel</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Notification Upload Modal -->
        <div id="notification-upload-modal" class="upload-modal">
            <div class="upload-modal-content">
                <div class="upload-modal-header">
                    <h3>Upload File to Step</h3>
                    <button class="upload-modal-close" id="notification-upload-close">&times;</button>
                </div>
                <form id="notification-upload-form" enctype="multipart/form-data">
                    <div class="upload-form-group">
                        <label>File Information:</label>
                        <div id="upload-file-info" style="background: #f8f9fa; padding: 10px; border-radius: 3px; margin-bottom: 10px;">
                            <!-- File info will be populated by JavaScript -->
                        </div>
                    </div>
                    <div class="upload-form-group">
                        <label for="notification-upload-file">Select File to Upload:</label>
                        <input type="file" id="notification-upload-file" name="file">
                    </div>
                    <div class="upload-form-group">
                        <label for="notification-upload-status">New Step Status:</label>
                        <select id="notification-upload-status" name="status" required>
                            <option value="Completed">Completed</option>
                            <option value="In Progress">In Progress</option>
                            <option value="On Hold">On Hold</option>
                            <option value="Rejected">Rejected</option>
                        </select>
                    </div>
                    <div class="upload-form-group">
                        <label for="notification-upload-comment">Comment (optional):</label>
                        <input type="text" id="notification-upload-comment" name="comment" placeholder="Enter a comment about this update">
                    </div>
                    <div class="upload-form-actions">
                        <button type="submit" class="upload-submit-btn">
                            <span class="normal-text">Upload & Update Step</span>
                            <span class="loading-text">Uploading...</span>
                        </button>
                        <button type="button" class="upload-cancel-btn" id="notification-upload-cancel">Cancel</button>
                    </div>
                </form>
            </div>
        </div>

        <div class="table-container">
            <!-- Filter Row -->
            <div style="margin-bottom: 10px; display: flex; gap: 10px; flex-wrap: wrap;">
                <label>Supplier:
                    <select id="filter-supplier">
                        <option value="">All</option>
                        {% for type in files.values()|map(attribute='supplier')|unique %}
                        <option value="{{ type }}">{{ type }}</option>
                        {% endfor %}
                    </select>
                </label>
                <label>Process Type:
                    <select id="filter-process-type">
                        <option value="">All</option>
                        {% for type in files.values()|map(attribute='process_type')|unique %}
                        <option value="{{ type }}">{{ type }}</option>
                        {% endfor %}
                    </select>
                </label>
                <label>Current Step:
                    <select id="filter-step">
                        <option value="">All</option>
                        {% for type in files.values()|map(attribute='current_step')|unique %}
                        <option value="{{ type }}">{{ type|capitalize }}</option>
                        {% endfor %}
                    </select>
                </label>
                <label>Status:
                    <select id="filter-status">
                        <option value="">All</option>
                        <option value="Completed">Completed</option>
                        <option value="In Progress">In Progress</option>
                    </select>
                </label>
            </div>
            <table class="files-table" id="main-table">
                <thead>
                    <tr>
                        <th data-col="0">Supplier <button class="sort-btn" data-col="0">⇅</button></th>
                        <th data-col="1">Process Type <button class="sort-btn" data-col="1">⇅</button></th>
                        <th data-col="2">Filename <button class="sort-btn" data-col="2">⇅</button></th>
                        <th data-col="3">Current Step <button class="sort-btn" data-col="3">⇅</button></th>
                        <th data-col="4">Creation Time <button class="sort-btn" data-col="4">⇅</button></th>
                        <th data-col="5">Last Update <button class="sort-btn" data-col="5">⇅</button></th>
                        <th data-col="6">Total Time <button class="sort-btn" data-col="6">⇅</button></th>
                        <th data-col="7">Current Step Time <button class="sort-btn" data-col="7">⇅</button></th>
                        <th data-col="8">Status <button class="sort-btn" data-col="8">⇅</button></th>
                        <th data-col="9">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for file_id, file in files.items() %}
                    <tr>
                        <td>{{ file.supplier }}</td>
                        <td>{{ file.process_type|default('Unknown', true) }}</td>
                        <td>{{ file.original_filename }}</td>
                        <td>{{ file.current_step|capitalize }}</td>
                        <td>
                            {% if file.creation_time %}
                                {{ file.creation_time | strftime }}
                            {% else %}
                                {% if file.history|length > 0 %}
                                    {{ file.history[0].timestamp | strftime }}
                                {% else %}
                                    N/A
                                {% endif %}
                            {% endif %}
                        </td>
                        <td>
                            {% if file.history|length > 0 %}
                                {{ file.history[-1].timestamp|strftime }}
                            {% else %}
                                N/A
                            {% endif %}
                        </td>
                        <td>
                            {% if file.creation_time and file.history|length > 0 %}
                                {% set creation = file.creation_time %}
                                {% set last_update = file.history[-1].timestamp %}
                                {{ ((last_update|string)[:19]|to_datetime - (creation|string)[:19]|to_datetime)|string|replace('days', 'd')|replace('day', 'd')  }}
                            {% elif file.history|length > 1 %}
                                {% set creation = file.history[0].timestamp %}
                                {% set last_update = file.history[-1].timestamp %}
                                {{ ((last_update|string)[:19]|to_datetime - (creation|string)[:19]|to_datetime)|string|replace('days', 'd')|replace('day', 'd') }}
                            {% else %}
                                N/A
                            {% endif %}
                        </td>
                        <td class="{% if current_step_times[file_id].is_overdue %}overdue{% endif %}">

                            {{ '%02d:%02d:%02d'|format(current_step_times[file_id].total_time_worked // 1440, (current_step_times[file_id].total_time_worked % 1440) // 60, current_step_times[file_id].total_time_worked % 60) }}
                            {% if current_step_times[file_id].is_overdue %}
                                <span class="overdue-indicator" title="Overdue! Assigned time exceeded.">⚠️</span>
                            {% endif %}
                            {% if current_step_times[file_id].assigned_time > 0 %}
                                <span class="assigned-time">({{ '%02d:%02d:%02d'|format(current_step_times[file_id].assigned_time // 1440, (current_step_times[file_id].assigned_time % 1440) // 60, current_step_times[file_id].assigned_time % 60) }} assigned)</span>
                            {% endif %}
                        </td>
                        <td>
                            {% set current_step = file.current_step %}
                            {% set ns = namespace(step_completed=true) %}

                            {% for step_name, entry in file.step_statuses.items() %}
                                {% if entry.status != 'Completed' %}

                                    {% set ns.step_completed = false %}
                                {% endif %}
                            {% endfor %}

                            {% if ns.step_completed %}
                                <span class="status-completed">Completed</span>
                            {% else %}
                                <span class="status-in-progress">In Progress</span>
                            {% endif %}
                        </td>
                        <td>
                            <a href="{{ url_for('file_pipeline', file_id=file_id) }}" class="btn btn-small">View Pipeline</a>
                            {% if is_admin %}
                            <button class="btn btn-small btn-danger delete-file-btn" data-file-id="{{ file_id }}" data-filename="{{ file.original_filename }}">Delete</button>
                            {% endif %}
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="10" class="no-data">No files in process.</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // Show/hide upload form
        document.getElementById('upload-new-btn').addEventListener('click', function() {
            // Reset the form
            document.getElementById('upload-form').reset();

            // Reset the upload button state
            const submitBtn = document.getElementById('upload-submit-btn');
            submitBtn.classList.remove('is-loading');
            submitBtn.disabled = false;
            submitBtn.querySelector('.spinner').style.display = 'none';

            // Show the form
            document.getElementById('upload-form-container').classList.remove('hidden');
        });

        // Save all data button handler
        const saveAllDataBtn = document.getElementById('save-all-data-btn');
        if (saveAllDataBtn) {
            saveAllDataBtn.addEventListener('click', function() {
                // Show loading state
                const originalText = this.textContent;
                this.textContent = 'Saving...';
                this.disabled = true;

                // Send request to save all data
                fetch('/save_all_data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('All data saved successfully');
                    } else {
                        alert('Failed to save data: ' + data.message);
                    }
                    // Reset button state
                    this.textContent = originalText;
                    this.disabled = false;
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while saving data');
                    // Reset button state
                    this.textContent = originalText;
                    this.disabled = false;
                });
            });
        }

        document.querySelectorAll('.close-modal, .close-modal-btn').forEach(element => {
            element.addEventListener('click', function() {
                document.getElementById('upload-form-container').classList.add('hidden');
            });
        });

        // Handle form submission with loading indicator
        document.getElementById('upload-form').addEventListener('submit', function(e) {
            const submitBtn = document.getElementById('upload-submit-btn');
            const spinner = submitBtn.querySelector('.spinner');

            // Show loading state
            submitBtn.classList.add('is-loading');
            submitBtn.disabled = true;
            spinner.style.display = 'inline-block';

            // Form will submit normally
            // The loading state will remain until the page reloads with the response
        });

        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            const modal = document.getElementById('upload-form-container');
            if (event.target === modal) {
                modal.classList.add('hidden');
            }
        });

        // Function to convert minutes to DD:HH:MM format
        function minutesToTime(totalMinutes) {
            if (isNaN(totalMinutes) || totalMinutes < 0) {
                return '00:00:00';
            }

            const days = Math.floor(totalMinutes / (24 * 60));
            const hours = Math.floor((totalMinutes % (24 * 60)) / 60);
            const minutes = totalMinutes % 60;

            return `${days.toString().padStart(2, '0')}:${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
        }

        // Periodically update current step times
        function updateCurrentStepTimes() {
            // Get all file rows
            const fileRows = document.querySelectorAll('.files-table tbody tr');

            // For each file row, fetch the latest time data
            fileRows.forEach(row => {
                // Skip the "No files" row
                if (row.querySelector('.no-data')) {
                    return;
                }

                // Get the file ID from the delete button or view pipeline link
                let fileId = null;
                const deleteBtn = row.querySelector('.delete-file-btn');
                if (deleteBtn) {
                    fileId = deleteBtn.getAttribute('data-file-id');
                } else {
                    const viewLink = row.querySelector('a[href*="file_pipeline"]');
                    if (viewLink) {
                        const href = viewLink.getAttribute('href');
                        fileId = href.split('/').pop();
                    }
                }

                if (fileId) {
                    // Fetch the latest time data for this file
                    fetch(`/api/step_times/${fileId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.step_times) {
                            // Get the current step
                            const currentStepCell = row.cells[3];
                            const currentStep = currentStepCell.textContent.trim().toLowerCase();

                            // Find the time data for the current step
                            const timeData = data.step_times[currentStep];
                            if (timeData) {
                                // Get the current step time cell (8th column, index 7)
                                const timeCell = row.cells[7];
                                if (timeCell) {
                                    // Convert minutes to DD:HH:MM format
                                    const totalTimeWorked = timeData.total_time_worked;
                                    const formattedTime = minutesToTime(totalTimeWorked);

                                    // Update the time worked value
                                    let timeWorkedText = formattedTime;

                                    // Add assigned time if available
                                    const assignedTime = timeData.assigned_time || 0;
                                    if (assignedTime > 0) {
                                        const formattedAssignedTime = minutesToTime(assignedTime);
                                        timeWorkedText += ` <span class="assigned-time">(${formattedAssignedTime} assigned)</span>`;
                                    }

                                    // Add overdue indicator if needed
                                    if (timeData.is_overdue) {
                                        timeCell.classList.add('overdue');
                                        timeWorkedText += ` <span class="overdue-indicator" title="Overdue! Assigned time exceeded.">⚠️</span>`;
                                    } else {
                                        timeCell.classList.remove('overdue');
                                    }

                                    timeCell.innerHTML = timeWorkedText;
                                }
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error updating step times:', error);
                    });
                }
            });
        }

        // Update times immediately and then every minute
        updateCurrentStepTimes();
        setInterval(updateCurrentStepTimes, 60000); // 60000 ms = 1 minute

        // Delete file handling
        document.querySelectorAll('.delete-file-btn').forEach(button => {
            button.addEventListener('click', function() {
                const fileId = this.getAttribute('data-file-id');
                const filename = this.getAttribute('data-filename');

                if (confirm(`Are you sure you want to delete the file "${filename}"? This action cannot be undone and will remove all associated data.`)) {
                    // Send delete request to server
                    fetch('/delete_file', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            file_id: fileId
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Remove row from table
                            this.closest('tr').remove();
                            alert('File deleted successfully');

                            // If no more files, show the "No files" message
                            const tbody = document.querySelector('.files-table tbody');
                            if (tbody.children.length === 0) {
                                const noDataRow = document.createElement('tr');
                                noDataRow.innerHTML = '<td colspan="10" class="no-data">No files in process.</td>';
                                tbody.appendChild(noDataRow);
                            }
                        } else {
                            alert('Failed to delete file: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('An error occurred while deleting the file');
                    });
                }
            });
        });

        // Filtering logic
        function filterTable() {
            const supplier = document.getElementById('filter-supplier').value;
            const processType = document.getElementById('filter-process-type').value;
            const step = document.getElementById('filter-step').value;
            const status = document.getElementById('filter-status').value;
            const table = document.getElementById('main-table');
            const rows = table.tBodies[0].rows;
            for (let i = 0; i < rows.length; i++) {
                const cells = rows[i].cells;
                if (cells.length < 10) continue; // skip no-data row
                let show = true;
                if (supplier && cells[0].textContent.trim() !== supplier) show = false;
                if (processType && cells[1].textContent.trim() !== processType) show = false;
                if (step && cells[3].textContent.trim().toLowerCase() !== step.toLowerCase()) show = false;
                if (status) {
                    const statusText = cells[8].textContent.trim();
                    if (status === 'Completed' && statusText !== 'Completed') show = false;
                    if (status === 'In Progress' && statusText !== 'In Progress') show = false;
                }
                rows[i].style.display = show ? '' : 'none';
            }
        }
        document.getElementById('filter-supplier').addEventListener('change', filterTable);
        document.getElementById('filter-process-type').addEventListener('change', filterTable);
        document.getElementById('filter-step').addEventListener('change', filterTable);
        document.getElementById('filter-status').addEventListener('change', filterTable);

        // Sorting logic
        let sortCol = null;
        let sortAsc = true;
        document.querySelectorAll('.sort-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const col = parseInt(this.getAttribute('data-col'));
                const table = document.getElementById('main-table');
                const tbody = table.tBodies[0];
                const rows = Array.from(tbody.rows).filter(r => r.cells.length === 10);
                if (sortCol === col) sortAsc = !sortAsc; else { sortCol = col; sortAsc = true; }
                rows.sort((a, b) => {
                    let v1 = a.cells[col].textContent.trim();
                    let v2 = b.cells[col].textContent.trim();
                    // Try to parse as date or number
                    if (!isNaN(Date.parse(v1)) && !isNaN(Date.parse(v2))) {
                        v1 = new Date(v1); v2 = new Date(v2);
                    } else if (!isNaN(v1) && !isNaN(v2)) {
                        v1 = parseFloat(v1); v2 = parseFloat(v2);
                    }
                    if (v1 < v2) return sortAsc ? -1 : 1;
                    if (v1 > v2) return sortAsc ? 1 : -1;
                    return 0;
                });
                rows.forEach(row => tbody.appendChild(row));
            });
        });

        // Notification system
        let notificationDropdownOpen = false;

        // Load notifications on page load
        loadNotifications();

        // Toggle notification dropdown
        document.getElementById('notification-btn').addEventListener('click', function(e) {
            e.stopPropagation();
            notificationDropdownOpen = !notificationDropdownOpen;
            const dropdown = document.getElementById('notification-dropdown');

            if (notificationDropdownOpen) {
                dropdown.classList.add('show');
                loadNotifications(); // Refresh notifications when opened
            } else {
                dropdown.classList.remove('show');
            }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (notificationDropdownOpen && !e.target.closest('.notification-container')) {
                document.getElementById('notification-dropdown').classList.remove('show');
                notificationDropdownOpen = false;
            }
        });

        // Mark all notifications as read
        document.getElementById('mark-all-read-btn').addEventListener('click', function() {
            fetch('/api/notifications/mark_all_read', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadNotifications(); // Refresh notifications
                }
            })
            .catch(error => {
                console.error('Error marking all notifications as read:', error);
            });
        });

        // Load notifications function
        function loadNotifications() {
            fetch('/api/notifications')
                .then(response => response.json())
                .then(data => {
                    updateNotificationBadge(data.unread_count);
                    displayNotifications(data.notifications);
                })
                .catch(error => {
                    console.error('Error loading notifications:', error);
                    document.getElementById('notification-list').innerHTML =
                        '<div class="no-notifications">Error loading notifications</div>';
                });
        }

        // Update notification badge
        function updateNotificationBadge(count) {
            const badge = document.getElementById('notification-badge');
            if (count > 0) {
                badge.textContent = count > 99 ? '99+' : count;
                badge.classList.add('show');
            } else {
                badge.classList.remove('show');
            }
        }

        // Display notifications
        function displayNotifications(notifications) {
            const notificationList = document.getElementById('notification-list');

            if (notifications.length === 0) {
                notificationList.innerHTML = '<div class="no-notifications">No notifications</div>';
                return;
            }

            let html = '';
            notifications.forEach(notification => {
                const isUnread = !notification.read;
                const timeAgo = formatTimeAgo(notification.timestamp);

                html += `
                    <div class="notification-item ${isUnread ? 'unread' : ''}" data-id="${notification.id}">
                        <div class="notification-title">${notification.title}</div>
                        <div class="notification-message">${notification.message}</div>
                        <div class="notification-time">${timeAgo}</div>
                        <div class="notification-actions">
                            ${isUnread ? `<button class="mark-read-btn" onclick="markNotificationRead('${notification.id}')">Mark as Read</button>` : ''}
                            ${notification.file_id ? `<button class="open-file-btn" onclick="openFile('${notification.file_id}')">Open File</button>` : ''}
                            ${notification.file_id ? `<button class="download-prev-btn" onclick="downloadPreviousStepFile('${notification.file_id}')">Download Previous</button>` : ''}
                            ${notification.file_id ? `<button class="upload-btn" onclick="openUploadModal('${notification.file_id}', '${notification.step}')">Upload to Step</button>` : ''}
                        </div>
                    </div>
                `;
            });

            notificationList.innerHTML = html;
        }

        // Mark single notification as read
        function markNotificationRead(notificationId) {
            fetch('/api/notifications/mark_read', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    notification_id: notificationId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadNotifications(); // Refresh notifications
                }
            })
            .catch(error => {
                console.error('Error marking notification as read:', error);
            });
        }

        // Open file from notification
        function openFile(fileId) {
            window.location.href = `/file/${fileId}`;
        }

        // Format time ago
        function formatTimeAgo(timestamp) {
            const now = new Date();
            const time = new Date(timestamp);
            const diffInSeconds = Math.floor((now - time) / 1000);

            if (diffInSeconds < 60) {
                return 'Just now';
            } else if (diffInSeconds < 3600) {
                const minutes = Math.floor(diffInSeconds / 60);
                return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
            } else if (diffInSeconds < 86400) {
                const hours = Math.floor(diffInSeconds / 3600);
                return `${hours} hour${hours > 1 ? 's' : ''} ago`;
            } else {
                const days = Math.floor(diffInSeconds / 86400);
                return `${days} day${days > 1 ? 's' : ''} ago`;
            }
        }

        // Refresh notifications every 30 seconds
        setInterval(loadNotifications, 30000);

        // Download previous step file for a specific file
        function downloadPreviousStepFile(fileId) {
            // Show loading state
            const button = event.target;
            const originalText = button.textContent;
            button.disabled = true;
            button.textContent = 'Downloading...';

            fetch('/download_single_previous_step_file', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    file_id: fileId
                })
            })
            .then(response => {
                if (response.ok) {
                    // Get filename from response headers
                    const contentDisposition = response.headers.get('Content-Disposition');
                    let filename = 'previous_step_file.zip';
                    if (contentDisposition) {
                        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
                        if (filenameMatch) {
                            filename = filenameMatch[1];
                        }
                    }

                    // Create blob and download
                    return response.blob().then(blob => {
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.style.display = 'none';
                        a.href = url;
                        a.download = filename;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);
                    });
                } else {
                    return response.json().then(data => {
                        throw new Error(data.message || 'Failed to download file');
                    });
                }
            })
            .then(() => {
                // Reset button state
                button.disabled = false;
                button.textContent = originalText;
            })
            .catch(error => {
                console.error('Error downloading file:', error);
                alert('Error downloading file: ' + error.message);
                // Reset button state
                button.disabled = false;
                button.textContent = originalText;
            });
        }

        // Open upload modal for a specific file and step
        let currentUploadFileId = null;
        let currentUploadStep = null;

        function openUploadModal(fileId, step) {
            currentUploadFileId = fileId;
            currentUploadStep = step;

            // Get file information
            fetch(`/api/file_info/${fileId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const fileInfo = data.file;
                        document.getElementById('upload-file-info').innerHTML = `
                            <strong>File:</strong> ${fileInfo.original_filename}<br>
                            <strong>Supplier:</strong> ${fileInfo.supplier}<br>
                            <strong>Process Type:</strong> ${fileInfo.process_type}<br>
                            <strong>Current Step:</strong> ${fileInfo.current_step}<br>
                            <strong>Step Status:</strong> ${fileInfo.step_statuses[step]?.status || 'Unknown'}
                        `;

                        // Show modal
                        document.getElementById('notification-upload-modal').style.display = 'block';
                    } else {
                        alert('Error loading file information: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error loading file info:', error);
                    alert('Error loading file information');
                });
        }

        // Close upload modal
        document.getElementById('notification-upload-close').addEventListener('click', function() {
            console.log('Upload form cancelled');
            document.getElementById('notification-upload-modal').style.display = 'none';
            currentUploadFileId = null;
            currentUploadStep = null;
        });

        document.getElementById('notification-upload-cancel').addEventListener('click', function() {
            console.log('Upload form cancelled');
            document.getElementById('notification-upload-modal').style.display = 'none';
            currentUploadFileId = null;
            currentUploadStep = null;
        });

        // Handle upload form submission
        document.getElementById('notification-upload-form').addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('Upload form submitted');

            if (!currentUploadFileId || !currentUploadStep) {
                alert('Error: No file or step selected');
                return;
            }

            const formData = new FormData();
            const fileInput = document.getElementById('notification-upload-file');
            const statusSelect = document.getElementById('notification-upload-status');
            const commentInput = document.getElementById('notification-upload-comment');

            if (!fileInput.files[0]) {
                //alert('Please select a file to upload');
                
                formData.append('file', '');
                //return;
            }else{
                formData.append('file', fileInput.files[0]);
            }

            formData.append('file_id', currentUploadFileId);
            formData.append('step', currentUploadStep);
            formData.append('status', statusSelect.value);
            formData.append('comment', commentInput.value);

            // Show loading state
            const submitBtn = this.querySelector('.upload-submit-btn');
            submitBtn.disabled = true;
            submitBtn.querySelector('.normal-text').style.display = 'none';
            submitBtn.querySelector('.loading-text').style.display = 'inline';
            console.log('Form data:', formData);
            
            fetch('/upload_to_step', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('File uploaded and step updated successfully!');
                    // Close modal
                    document.getElementById('notification-upload-modal').style.display = 'none';
                    // Refresh notifications
                    loadNotifications();
                    // Refresh page to show updated file status
                    window.location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error uploading file:', error);
                alert('Error uploading file');
            })
            .finally(() => {
                // Reset button state
                submitBtn.disabled = false;
                submitBtn.querySelector('.normal-text').style.display = 'inline';
                submitBtn.querySelector('.loading-text').style.display = 'none';

                // Reset form
                this.reset();
                currentUploadFileId = null;
                currentUploadStep = null;
            });
        });

        // Download previous step files functionality
        document.getElementById('download-previous-step-btn').addEventListener('click', function() {
            // No need to get filters or visible files - we'll process all files on server side
            // based on user's assigned steps

            // Show loading state
            this.disabled = true;
            this.textContent = 'Preparing Download...';

            // Send request to download previous step files for user's assigned steps
            fetch('/download_previous_step_files', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    // No file_ids or filters needed - server will determine based on user assignments
                })
            })
            .then(response => {
                if (response.ok) {
                    // Get filename from response headers
                    const contentDisposition = response.headers.get('Content-Disposition');
                    let filename = 'previous_step_files.zip';
                    if (contentDisposition) {
                        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
                        if (filenameMatch) {
                            filename = filenameMatch[1];
                        }
                    }

                    // Create blob and download
                    return response.blob().then(blob => {
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.style.display = 'none';
                        a.href = url;
                        a.download = filename;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);
                    });
                } else {
                    return response.json().then(data => {
                        throw new Error(data.message || 'Failed to download files');
                    });
                }
            })
            .then(() => {
                // Reset button state
                this.disabled = false;
                this.textContent = 'Download Previous Step Files';
            })
            .catch(error => {
                console.error('Error downloading files:', error);
                alert('Error downloading files: ' + error.message);
                // Reset button state
                this.disabled = false;
                this.textContent = 'Download Previous Step Files';
            });
        });
    </script>
</body>
</html>