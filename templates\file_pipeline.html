<!DOCTYPE html>
<html>
<head>
    <title>File Pipeline - {{ file.supplier }}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <style>
        .overdue {
            color: #e74c3c;
            font-weight: bold;
        }

        .overdue-indicator {
            color: #e74c3c;
            font-size: 1.2em;
            margin-left: 5px;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { opacity: 0.5; }
            50% { opacity: 1; }
            100% { opacity: 0.5; }
        }

        /* Loading spinner */
        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
            vertical-align: middle;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .loading-text {
            display: none;
        }

        .is-loading .loading-text {
            display: inline;
        }

        .is-loading .normal-text {
            display: none;
        }

        /* Notification styles */
        .notification-container {
            position: relative;
            display: inline-block;
        }

        .notification-btn {
            position: relative;
            background: #3498db;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }

        .notification-btn:hover {
            background: #2980b9;
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 12px;
            font-weight: bold;
            min-width: 18px;
            text-align: center;
            display: none;
        }

        .notification-badge.show {
            display: block;
        }

        .notification-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            width: 350px;
            max-height: 400px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .notification-dropdown.show {
            display: block;
        }

        .notification-header {
            padding: 15px;
            border-bottom: 1px solid #eee;
            background: #f8f9fa;
            border-radius: 5px 5px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .notification-header h3 {
            margin: 0;
            font-size: 16px;
            color: #333;
        }

        .mark-all-read-btn {
            background: #27ae60;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }

        .mark-all-read-btn:hover {
            background: #219a52;
        }

        .notification-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .notification-item {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background 0.2s;
        }

        .notification-item:hover {
            background: #f8f9fa;
        }

        .notification-item.unread {
            background: #e8f4fd;
            border-left: 3px solid #3498db;
        }

        .notification-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
            font-size: 14px;
        }

        .notification-message {
            color: #666;
            font-size: 13px;
            line-height: 1.4;
            margin-bottom: 5px;
        }

        .notification-time {
            color: #999;
            font-size: 11px;
        }

        .notification-actions {
            margin-top: 8px;
            display: flex;
            gap: 8px;
        }

        .mark-read-btn {
            background: #95a5a6;
            color: white;
            border: none;
            padding: 3px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 11px;
        }

        .mark-read-btn:hover {
            background: #7f8c8d;
        }

        .open-file-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 3px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 11px;
        }

        .open-file-btn:hover {
            background: #2980b9;
        }

        .no-notifications {
            padding: 20px;
            text-align: center;
            color: #999;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>File Processing Pipeline</h1>
        <div class="user-info">
            <span>Welcome, {{ username }}</span>

            <!-- Notification Button -->
            <div class="notification-container">
                <button class="notification-btn" id="notification-btn">
                    🔔
                    <span class="notification-badge" id="notification-badge">0</span>
                </button>
                <div class="notification-dropdown" id="notification-dropdown">
                    <div class="notification-header">
                        <h3>Notifications</h3>
                        <button class="mark-all-read-btn" id="mark-all-read-btn">Mark All Read</button>
                    </div>
                    <div class="notification-list" id="notification-list">
                        <div class="no-notifications">Loading notifications...</div>
                    </div>
                </div>
            </div>

            <a href="{{ url_for('logout') }}" class="btn btn-small">Logout</a>
            {% if is_admin %}
            <a href="{{ url_for('register') }}" class="btn btn-small">Register User</a>
            {% endif %}
        </div>
    </div>

    {% with messages = get_flashed_messages() %}
    {% if messages %}
    <div class="flash-messages">
        {% for message in messages %}
        <div class="flash-message">{{ message }}</div>
        {% endfor %}
    </div>
    {% endif %}
    {% endwith %}

    <div class="container">
        <div class="actions-bar">
            <h2>Pipeline for {{ file.supplier }} - {{ file.original_filename }}</h2>
            <div>
                <a href="{{ url_for('index') }}" class="btn btn-secondary">Back to Overview</a>
                {% if is_admin %}
                <a href="{{ url_for('manage_file_steps', file_id=file_id) }}" class="btn">Manage Steps</a>
                {% endif %}
            </div>
        </div>

        <div class="file-info">
            <p><strong>File ID:</strong> {{ file_id }}</p>
            <p><strong>Current Step:</strong> <span class="current-step-indicator">{{ file.current_step|capitalize }}</span></p>
            <p><strong>Creation Time:</strong>
                {% if file.creation_time %}
                    {{ file.creation_time }}
                {% else %}
                    {% if file.history|length > 0 %}
                        {{ file.history[0].timestamp }}
                    {% else %}
                        N/A
                    {% endif %}
                {% endif %}
            </p>
            <p><strong>Last Update:</strong>
                {% if file.history|length > 0 %}
                    {% if file.history[-1].timestamp %}
                        {{ file.history[-1].timestamp|strftime }}
                    {% else %}
                        Never
                    {% endif %}
                {% else %}
                    N/A
                {% endif %}
            </p>
            <p><strong>Total Time:</strong>
                {% if file.creation_time and file.history|length > 0 %}
                    {% set creation = file.creation_time %}
                    {% set last_update = file.history[-1].timestamp %}
                    {{ ((last_update|string)[:19]|to_datetime - (creation|string)[:19]|to_datetime)|string|replace('days', 'd')|replace('day', 'd') }}
                {% elif file.history|length > 1 %}
                    {% set creation = file.history[0].timestamp %}
                    {% set last_update = file.history[-1].timestamp %}
                    {{ ((last_update|string)[:19]|to_datetime - (creation|string)[:19]|to_datetime)|string|replace('days', 'd')|replace('day', 'd') }}
                {% else %}
                    N/A
                {% endif %}
            </p>
            <p><strong>Process Progress:</strong>
                <div class="progress-bar">
                    {% set ns = namespace(step_completed=true,completed_count=0) %}
                    {% set ns.step_completed = false %}
                    {% set ns.completed_count = 0 %}

                    {% for step_name, entry in file.step_statuses.items() %}
                        {% set ns.step_completed = false %}
                        {% if entry.status == 'Completed' %}
                            {% set ns.step_completed = true %}
                            {% set ns.completed_count = ns.completed_count + 1 %}
                        {% endif %}


                        <div class="progress-step
                            {% if step_completed %}completed{% endif %}
                            {% if step_name == file.current_step %}current{% endif %}"
                            title="{{ step|capitalize }}">
                        </div>
                    {% endfor %}
                    <div class="progress-text">{{ ns.completed_count }} of {{ steps|length }} steps completed</div>
                </div>
            </p>
        </div>

        <div class="pipeline-wrapper">
            <div class="pipeline-container">
                {% for step in steps %}
                    {% set step_data = step_statuses[step] %}
                    <div class="pipeline-step {% if step == file.current_step %}current{% endif %}" id="step-{{ loop.index }}">
                        <div class="pipeline-card">
                            <h3>{{ step|capitalize }}</h3>
                            <div class="status-indicator status-{{ step_data.status|lower|replace(' ', '-') }}">
                                {{ step_data.status }}
                            </div>
                            <div class="card-details">
                                <p><strong>Last Update:</strong> {% if step_data.last_update %}
                                                                {{ step_data.last_update | strftime }}
                                                                {% else %}
                                                                Never
                                                                {% endif %}</p>
                                <p><strong>Updated By:</strong> {{ step_data.user|default('N/A', true) }}</p>
                                <p><strong>Assigned Users:</strong>
                                    {% if file.step_assignments and step in file.step_assignments %}
                                        {{ file.step_assignments[step]|join(', ')|default('None', true) }}
                                    {% else %}
                                        None
                                    {% endif %}
                                </p>
                                <p><strong>Assigned Time:</strong> {{ '%02d:%02d:%02d'|format(step_data.assigned_time // 1440, (step_data.assigned_time % 1440) // 60, step_data.assigned_time % 60) }}</p>
                                <p><strong>Time Worked:</strong>
                                    <span class="{% if step_data.is_overdue %}overdue{% endif %}">
                                        {{ '%02d:%02d:%02d'|format(step_data.total_time_worked // 1440, (step_data.total_time_worked % 1440) // 60, step_data.total_time_worked % 60) }}
                                        {% if step_data.is_overdue %}
                                            <span class="overdue-indicator" title="Overdue! Assigned time exceeded.">⚠️</span>
                                        {% endif %}
                                    </span>
                                </p>
                            </div>
                            <div class="card-actions">
                                {% if step_data.can_edit %}
                                <select class="status-select" data-step="{{ step }}" data-file-id="{{ file_id }}">
                                    <option value="Not Started" {% if step_data.status == 'Not Started' %}selected{% endif %}>Not Started</option>
                                    <option value="In Progress" {% if step_data.status == 'In Progress' %}selected{% endif %}>In Progress</option>
                                    <option value="Completed" {% if step_data.status == 'Completed' %}selected{% endif %}>Completed</option>
                                </select>
                                <button class="btn btn-small btn-upload upload-btn" data-step="{{ step }}" data-file-id="{{ file_id }}">
                                    <span class="normal-text">Upload</span>
                                </button>
                                {% endif %}
                                <button class="btn btn-small download-btn" data-step="{{ step }}">Download</button>
                                {% if is_admin %}
                                <button class="btn btn-small manage-users-btn" data-step="{{ step }}">Users</button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                {% endfor %}

                <!-- Connection lines drawn with SVG -->
                <svg class="pipeline-connections">
                    {% for i in range(1, steps|length) %}
                    <path class="connector-path" id="connector-{{ i }}" />
                    {% endfor %}
                </svg>
            </div>
        </div>

        <script>
            // Draw connection lines between steps
            function drawConnections() {
                const steps = document.querySelectorAll('.pipeline-step');
                if (steps.length <= 1) return;

                const svg = document.querySelector('.pipeline-connections');

                // Set SVG dimensions to match container
                const container = document.querySelector('.pipeline-container');
                svg.setAttribute('width', container.offsetWidth);
                svg.setAttribute('height', container.offsetHeight);

                // Draw connections between each step
                for (let i = 0; i < steps.length - 1; i++) {
                    const start = steps[i];
                    const end = steps[i + 1];

                    const startRect = start.getBoundingClientRect();
                    const endRect = end.getBoundingClientRect();

                    // Calculate positions relative to the SVG
                    const startX = startRect.right - container.getBoundingClientRect().left;
                    const startY = startRect.top + (startRect.height / 2) - container.getBoundingClientRect().top;
                    const endX = endRect.left - container.getBoundingClientRect().left;
                    const endY = endRect.top + (endRect.height / 2) - container.getBoundingClientRect().top;

                    // Create curved path
                    const midX = (startX + endX) / 2;
                    const path = `M${startX},${startY} C${midX},${startY} ${midX},${endY} ${endX},${endY}`;

                    // Update path
                    const connector = document.getElementById(`connector-${i+1}`);
                    connector.setAttribute('d', path);

                    // Add arrowhead
                    const marker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
                    marker.setAttribute('id', `arrowhead-${i+1}`);
                    marker.setAttribute('markerWidth', '10');
                    marker.setAttribute('markerHeight', '7');
                    marker.setAttribute('refX', '10');
                    marker.setAttribute('refY', '3.5');
                    marker.setAttribute('orient', 'auto');

                    const polygon = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
                    polygon.setAttribute('points', '0 0, 10 3.5, 0 7');
                    polygon.setAttribute('fill', '#3498db');

                    marker.appendChild(polygon);
                    svg.appendChild(marker);

                    connector.setAttribute('marker-end', `url(#arrowhead-${i+1})`);
                }
            }

            // Draw connections when page loads and when window resizes
            window.addEventListener('load', drawConnections);
            window.addEventListener('resize', drawConnections);
        </script>

        <!-- Download Modal -->
        <div id="download-modal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Download Files</h2>
                    <span class="close-modal">&times;</span>
                </div>
                <div class="modal-body">
                    <p>Select a file version to download:</p>
                    <div id="download-files-list">
                        <!-- Will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Upload Modal -->
        <div id="upload-modal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Upload File for <span id="upload-step-name">Step</span></h2>
                    <span class="close-modal">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="upload-form" action="/upload_to_step" method="post" enctype="multipart/form-data">
                        <input type="hidden" id="upload-file-id" name="file_id">
                        <input type="hidden" id="upload-step" name="step">

                        <div class="form-group">
                            <label for="upload-file">Select File:</label>
                            <input type="file" id="upload-file" name="file" required>
                        </div>

                        <div class="form-group">
                            <label for="upload-comment">Comment (optional):</label>
                            <textarea id="upload-comment" name="comment" rows="3"></textarea>
                        </div>

                        <div class="form-actions">
                            <button type="submit" id="upload-submit-btn" class="btn">
                                <span class="spinner" style="display: none;"></span>
                                <span class="normal-text">Upload</span>
                                <span class="loading-text">Uploading...</span>
                            </button>
                            <button type="button" class="btn btn-secondary close-modal-btn">Cancel</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Manage Users Modal -->
        <div id="manage-users-modal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Manage Users for <span id="manage-users-step-name">Step</span></h2>
                    <span class="close-modal">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="manage-users-form" action="/manage_step_users/{{ file_id }}" method="post">
                        <input type="hidden" id="manage-users-step" name="step">

                        <div class="form-group">
                            <label>Assigned Users:</label>
                            <div class="checkbox-group" id="user-checkboxes">
                                <!-- Will be populated by JavaScript -->
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn">Save Changes</button>
                            <button type="button" class="btn btn-secondary close-modal-btn">Cancel</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Status change handling
        document.querySelectorAll('.status-select').forEach(select => {
            select.addEventListener('change', function() {
                const step = this.getAttribute('data-step');
                const fileId = this.getAttribute('data-file-id');
                const status = this.value;

                fetch('/update_status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        file_id: fileId,
                        step: step,
                        status: status
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update UI to reflect the change
                        const statusIndicator = this.closest('.pipeline-card').querySelector('.status-indicator');
                        statusIndicator.className = `status-indicator status-${status.toLowerCase().replace(' ', '-')}`;
                        statusIndicator.textContent = status;

                        // Show success message
                        alert('Status updated successfully');
                    } else {
                        alert('Failed to update status: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while updating the status');
                });
            });
        });

        // Download modal handling
        const downloadModal = document.getElementById('download-modal');
        const downloadFilesList = document.getElementById('download-files-list');

        document.querySelectorAll('.download-btn').forEach(button => {
            button.addEventListener('click', function() {
                const step = this.getAttribute('data-step');

                // Fetch file versions for this step
                fetch(`/api/file_versions/{{ file_id }}/${step}`)
                .then(response => response.json())
                .then(data => {
                    // Clear previous list
                    downloadFilesList.innerHTML = '';

                    if (data.versions && data.versions.length > 0) {
                        // Create list of file versions
                        data.versions.forEach(version => {
                            const item = document.createElement('div');
                            item.className = 'download-item';
                            item.innerHTML = `
                                <div class="download-info">
                                    <p><strong>Filename:</strong> ${version.filename}</p>
                                    <p><strong>Uploaded:</strong> ${version.timestamp}</p>
                                    <p><strong>User:</strong> ${version.user}</p>
                                    ${version.comment ? `<p><strong>Comment:</strong> ${version.comment}</p>` : ''}
                                </div>
                                <div class="download-actions">
                                    <a href="/download/${version.file_id}/${version.step}" class="btn btn-small">Download</a>
                                    {% if is_admin %}
                                    <button class="btn btn-small btn-danger delete-step-file-btn"
                                        data-file-id="${version.file_id}"
                                        data-step="${version.step}"
                                        data-timestamp="${version.timestamp}"
                                        data-filename="${version.filename}">Delete</button>
                                    {% endif %}
                                </div>
                            `;
                            downloadFilesList.appendChild(item);
                        });
                    } else {
                        downloadFilesList.innerHTML = '<p>No files available for this step.</p>';
                    }

                    // Show the modal
                    downloadModal.classList.remove('hidden');
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while fetching file versions');
                });
            });
        });

        // Upload modal handling
        const uploadModal = document.getElementById('upload-modal');
        const uploadStepName = document.getElementById('upload-step-name');
        const uploadFileId = document.getElementById('upload-file-id');
        const uploadStep = document.getElementById('upload-step');

        document.querySelectorAll('.upload-btn').forEach(button => {
            button.addEventListener('click', function() {
                const step = this.getAttribute('data-step');
                const fileId = this.getAttribute('data-file-id');

                // Set form values
                uploadStepName.textContent = step.charAt(0).toUpperCase() + step.slice(1);
                uploadFileId.value = fileId;
                uploadStep.value = step;

                // Reset the upload form and button state if it was previously submitted
                const submitBtn = document.getElementById('upload-submit-btn');
                submitBtn.classList.remove('is-loading');
                submitBtn.disabled = false;
                submitBtn.querySelector('.spinner').style.display = 'none';

                // Reset the file input
                document.getElementById('upload-file').value = '';

                // Reset the comment textarea
                document.getElementById('upload-comment').value = '';

                // Show the modal
                uploadModal.classList.remove('hidden');
            });
        });

        // Manage Users modal handling
        const manageUsersModal = document.getElementById('manage-users-modal');
        const manageUsersStepName = document.getElementById('manage-users-step-name');
        const manageUsersStep = document.getElementById('manage-users-step');
        const userCheckboxes = document.getElementById('user-checkboxes');

        document.querySelectorAll('.manage-users-btn').forEach(button => {
            button.addEventListener('click', function() {
                const step = this.getAttribute('data-step');

                // Set form values
                manageUsersStepName.textContent = step.charAt(0).toUpperCase() + step.slice(1);
                manageUsersStep.value = step;

                // Fetch users for this step
                fetch(`/api/step_users/{{ file_id }}/${step}`)
                .then(response => response.json())
                .then(data => {
                    // Clear previous checkboxes
                    userCheckboxes.innerHTML = '';

                    if (data.users && data.users.length > 0) {
                        // Create checkboxes for each user
                        data.users.forEach(user => {
                            const checkbox = document.createElement('div');
                            checkbox.className = 'checkbox-item';
                            checkbox.innerHTML = `
                                <input type="checkbox" id="user_${user.username}" name="assigned_users" value="${user.username}" ${user.assigned ? 'checked' : ''}>
                                <label for="user_${user.username}">${user.username}</label>
                            `;
                            userCheckboxes.appendChild(checkbox);
                        });
                    } else {
                        userCheckboxes.innerHTML = '<p>No users available.</p>';
                    }

                    // Show the modal
                    manageUsersModal.classList.remove('hidden');
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while fetching users');
                });
            });
        });

        // Handle upload form submission with loading indicator
        document.getElementById('upload-form').addEventListener('submit', function(e) {
            const submitBtn = document.getElementById('upload-submit-btn');
            const spinner = submitBtn.querySelector('.spinner');

            // Show loading state
            submitBtn.classList.add('is-loading');
            submitBtn.disabled = true;
            spinner.style.display = 'inline-block';

            // Form will submit normally
            // The loading state will remain until the page reloads with the response
        });

        // Close modals
        document.querySelectorAll('.close-modal, .close-modal-btn').forEach(element => {
            element.addEventListener('click', function() {
                downloadModal.classList.add('hidden');
                uploadModal.classList.add('hidden');
                manageUsersModal.classList.add('hidden');
            });
        });

        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === downloadModal) {
                downloadModal.classList.add('hidden');
            }
            if (event.target === uploadModal) {
                uploadModal.classList.add('hidden');
            }
            if (event.target === manageUsersModal) {
                manageUsersModal.classList.add('hidden');
            }
        });

        // Periodically update time worked for all steps
        // Function to convert minutes to DD:HH:MM format
        function minutesToTime(totalMinutes) {
            if (isNaN(totalMinutes) || totalMinutes < 0) {
                return '00:00:00';
            }

            const days = Math.floor(totalMinutes / (24 * 60));
            const hours = Math.floor((totalMinutes % (24 * 60)) / 60);
            const minutes = totalMinutes % 60;

            return `${days.toString().padStart(2, '0')}:${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
        }

        function updateStepTimes() {
            console.log('Updating step times...');
            fetch(`/api/step_times/{{ file_id }}`)
            .then(response => response.json())
            .then(data => {
                if (data.step_times) {
                    // Update each step's time worked display
                    for (const [step, timeData] of Object.entries(data.step_times)) {
                        // Find all pipeline steps
                        const pipelineSteps = document.querySelectorAll('.pipeline-step');

                        // Loop through each step to find the matching one
                        pipelineSteps.forEach(pipelineStep => {
                            const stepHeader = pipelineStep.querySelector('h3');
                            if (stepHeader && stepHeader.textContent.toLowerCase() === step.toLowerCase()) {
                                // Found the matching step, now find the time worked element
                                const timeWorkedElements = pipelineStep.querySelectorAll('p strong');

                                for (const strong of timeWorkedElements) {
                                    if (strong.textContent === 'Time Worked:') {
                                        // Found the time worked paragraph
                                        const timeWorkedSpan = strong.parentElement.querySelector('span');
                                        if (timeWorkedSpan) {
                                            // Convert minutes to DD:HH:MM format
                                            const formattedTime = minutesToTime(timeData.total_time_worked);

                                            // Update the time worked value (first text node)
                                            if (timeWorkedSpan.childNodes.length > 0) {
                                                timeWorkedSpan.childNodes[0].nodeValue = formattedTime;
                                            } else {
                                                timeWorkedSpan.textContent = formattedTime;
                                            }

                                            // Update overdue status
                                            if (timeData.is_overdue) {
                                                timeWorkedSpan.classList.add('overdue');

                                                // Add overdue indicator if it doesn't exist
                                                let overdueIndicator = timeWorkedSpan.querySelector('.overdue-indicator');
                                                if (!overdueIndicator) {
                                                    overdueIndicator = document.createElement('span');
                                                    overdueIndicator.className = 'overdue-indicator';
                                                    overdueIndicator.title = 'Overdue! Assigned time exceeded.';
                                                    overdueIndicator.textContent = '⚠️';
                                                    timeWorkedSpan.appendChild(overdueIndicator);
                                                }
                                            } else {
                                                timeWorkedSpan.classList.remove('overdue');

                                                // Remove overdue indicator if it exists
                                                const overdueIndicator = timeWorkedSpan.querySelector('.overdue-indicator');
                                                if (overdueIndicator) {
                                                    overdueIndicator.remove();
                                                }
                                            }
                                        }
                                        break;
                                    }
                                }
                            }
                        });
                    }
                }
            })
            .catch(error => {
                console.error('Error updating step times:', error);
            });
        }

        // Update times immediately and then every minute
        updateStepTimes();
        setInterval(updateStepTimes, 60000); // 60000 ms = 1 minute

        // Delete step file handling
        document.addEventListener('click', function(event) {
            if (event.target.classList.contains('delete-step-file-btn')) {
                const fileId = event.target.getAttribute('data-file-id');
                const step = event.target.getAttribute('data-step');
                const timestamp = event.target.getAttribute('data-timestamp');
                const filename = event.target.getAttribute('data-filename');

                if (confirm(`Are you sure you want to delete the file "${filename}" from step "${step}"? This action cannot be undone.`)) {
                    // Send delete request to server
                    fetch('/delete_step_file', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            file_id: fileId,
                            step: step,
                            timestamp: timestamp
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Remove item from list
                            event.target.closest('.download-item').remove();

                            // If no more files, show the "No files" message
                            if (downloadFilesList.children.length === 0) {
                                downloadFilesList.innerHTML = '<p>No files available for this step.</p>';
                            }

                            alert('File deleted successfully');
                        } else {
                            alert('Failed to delete file: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('An error occurred while deleting the file');
                    });
                }
            }
        });

        // Notification system
        let notificationDropdownOpen = false;

        // Load notifications on page load
        loadNotifications();

        // Toggle notification dropdown
        document.getElementById('notification-btn').addEventListener('click', function(e) {
            e.stopPropagation();
            notificationDropdownOpen = !notificationDropdownOpen;
            const dropdown = document.getElementById('notification-dropdown');

            if (notificationDropdownOpen) {
                dropdown.classList.add('show');
                loadNotifications(); // Refresh notifications when opened
            } else {
                dropdown.classList.remove('show');
            }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (notificationDropdownOpen && !e.target.closest('.notification-container')) {
                document.getElementById('notification-dropdown').classList.remove('show');
                notificationDropdownOpen = false;
            }
        });

        // Mark all notifications as read
        document.getElementById('mark-all-read-btn').addEventListener('click', function() {
            fetch('/api/notifications/mark_all_read', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadNotifications(); // Refresh notifications
                }
            })
            .catch(error => {
                console.error('Error marking all notifications as read:', error);
            });
        });

        // Load notifications function
        function loadNotifications() {
            fetch('/api/notifications')
                .then(response => response.json())
                .then(data => {
                    updateNotificationBadge(data.unread_count);
                    displayNotifications(data.notifications);
                })
                .catch(error => {
                    console.error('Error loading notifications:', error);
                    document.getElementById('notification-list').innerHTML =
                        '<div class="no-notifications">Error loading notifications</div>';
                });
        }

        // Update notification badge
        function updateNotificationBadge(count) {
            const badge = document.getElementById('notification-badge');
            if (count > 0) {
                badge.textContent = count > 99 ? '99+' : count;
                badge.classList.add('show');
            } else {
                badge.classList.remove('show');
            }
        }

        // Display notifications
        function displayNotifications(notifications) {
            const notificationList = document.getElementById('notification-list');

            if (notifications.length === 0) {
                notificationList.innerHTML = '<div class="no-notifications">No notifications</div>';
                return;
            }

            let html = '';
            notifications.forEach(notification => {
                const isUnread = !notification.read;
                const timeAgo = formatTimeAgo(notification.timestamp);

                html += `
                    <div class="notification-item ${isUnread ? 'unread' : ''}" data-id="${notification.id}">
                        <div class="notification-title">${notification.title}</div>
                        <div class="notification-message">${notification.message}</div>
                        <div class="notification-time">${timeAgo}</div>
                        <div class="notification-actions">
                            ${isUnread ? `<button class="mark-read-btn" onclick="markNotificationRead('${notification.id}')">Mark as Read</button>` : ''}
                            ${notification.file_id ? `<button class="open-file-btn" onclick="openFile('${notification.file_id}')">Open File</button>` : ''}
                        </div>
                    </div>
                `;
            });

            notificationList.innerHTML = html;
        }

        // Mark single notification as read
        function markNotificationRead(notificationId) {
            fetch('/api/notifications/mark_read', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    notification_id: notificationId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadNotifications(); // Refresh notifications
                }
            })
            .catch(error => {
                console.error('Error marking notification as read:', error);
            });
        }

        // Open file from notification
        function openFile(fileId) {
            window.location.href = `/file_pipeline/${fileId}`;
        }

        // Format time ago
        function formatTimeAgo(timestamp) {
            const now = new Date();
            const time = new Date(timestamp);
            const diffInSeconds = Math.floor((now - time) / 1000);

            if (diffInSeconds < 60) {
                return 'Just now';
            } else if (diffInSeconds < 3600) {
                const minutes = Math.floor(diffInSeconds / 60);
                return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
            } else if (diffInSeconds < 86400) {
                const hours = Math.floor(diffInSeconds / 3600);
                return `${hours} hour${hours > 1 ? 's' : ''} ago`;
            } else {
                const days = Math.floor(diffInSeconds / 86400);
                return `${days} day${days > 1 ? 's' : ''} ago`;
            }
        }

        // Refresh notifications every 30 seconds
        setInterval(loadNotifications, 30000);
    </script>
</body>
</html>
